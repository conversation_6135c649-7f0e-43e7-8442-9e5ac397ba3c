import path from 'node:path';
import { fileURLToPath } from 'node:url';
import { storybookTest } from '@storybook/addon-vitest/vitest-plugin';
import { defineConfig } from 'vitest/config';

const dirname =
  typeof __dirname !== 'undefined' ? __dirname : path.dirname(fileURLToPath(import.meta.url));

// Get the project root directory (one level up from config directory)
const projectRoot = path.resolve(dirname, '..');

export default defineConfig({
  plugins: [
    storybookTest({
      configDir: path.join(projectRoot, '.storybook'),
      storybookScript: 'npm run storybook',
    }),
  ],
  test: {
    root: projectRoot,
    browser: {
      enabled: true,
      headless: true,
      provider: 'playwright',
      instances: [{ browser: 'chromium' }],
    },
    setupFiles: ['config/vitest.setup.ts'],
    coverage: {
      enabled: true,
      include: ['src/**/*.{ts,tsx}'],
      exclude: [
        'src/**/*.stories.{ts,tsx}',
        'src/**/*.test.{ts,tsx}',
        'src/**/*.spec.{ts,tsx}',
        'src/**/constants/**',
        'src/**/types/**',
        'src/**/router/**',
        'src/**/styles/**',
        'src/**/mocks/**',
        'src/**/*.constant.ts',
        'src/**/*.constants.ts',
        'src/**/*.d.ts',
        'src/**/*.type.ts',
        'src/**/*.types.ts',
        'src/**/*.style.{ts,tsx}',
        'src/**/*.styles.{ts,tsx}',
        'src/**/*.mock.{ts,tsx}',
        'src/**/index.ts',
        'src/app/layout.tsx',
        'src/theme/**',
      ],
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      reportsDirectory: './coverage',
    },
  },
});
