import { defineWorkspace } from 'vitest/config';

export default defineWorkspace([
  {
    extends: './config/vitest.config.mts',
    test: {
      name: 'Unit Tests',
      // Don't override include - let the base config handle it
    },
  },
  {
    extends: './config/vitest.storybook.config.mts',
    test: {
      name: 'Storybook Tests',
      // Don't override include - let the storybookTest plugin handle story discovery
    },
  },
]);
